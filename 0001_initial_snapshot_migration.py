# Generated migration for libvirt snapshot models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VirtualMachine',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='虚拟机名称')),
                ('uuid', models.UUIDField(unique=True, verbose_name='libvirt UUID')),
                ('hypervisor_uri', models.CharField(max_length=255, verbose_name='Hypervisor连接URI')),
                ('status', models.CharField(choices=[('running', '运行中'), ('paused', '暂停'), ('shutoff', '关闭'), ('crashed', '崩溃')], max_length=20, verbose_name='虚拟机状态')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': '虚拟机',
                'verbose_name_plural': '虚拟机',
                'db_table': 'vm_virtual_machines',
            },
        ),
        migrations.CreateModel(
            name='Snapshot',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='快照名称')),
                ('description', models.TextField(blank=True, verbose_name='快照描述')),
                ('snapshot_type', models.CharField(choices=[('disk_only', '仅磁盘快照'), ('memory_only', '仅内存快照'), ('full_system', '完整系统快照')], max_length=20, verbose_name='快照类型')),
                ('state', models.CharField(choices=[('running', '运行中'), ('paused', '暂停'), ('shutoff', '关闭'), ('disk-snapshot', '磁盘快照')], max_length=20, verbose_name='快照时虚拟机状态')),
                ('is_current', models.BooleanField(default=False, verbose_name='是否为当前快照')),
                ('creation_time', models.DateTimeField(verbose_name='创建时间')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('libvirt_xml', models.TextField(verbose_name='libvirt XML配置')),
                ('domain_xml', models.TextField(blank=True, verbose_name='域XML配置')),
                ('cookie', models.TextField(blank=True, verbose_name='快照Cookie数据')),
                ('snapshot_path', models.CharField(blank=True, max_length=500, verbose_name='快照文件路径')),
                ('size_bytes', models.BigIntegerField(blank=True, null=True, verbose_name='快照大小')),
                ('deletion_in_progress', models.BooleanField(default=False, verbose_name='删除进行中')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='your_app.snapshot', verbose_name='父快照')),
                ('virtual_machine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='snapshots', to='your_app.virtualmachine', verbose_name='虚拟机')),
            ],
            options={
                'verbose_name': '快照',
                'verbose_name_plural': '快照',
                'db_table': 'vm_snapshots',
            },
        ),
        migrations.CreateModel(
            name='SnapshotOperation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('operation_type', models.CharField(choices=[('create', '创建'), ('delete', '删除'), ('revert', '恢复'), ('redefine', '重定义')], max_length=20, verbose_name='操作类型')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('running', '执行中'), ('success', '成功'), ('failed', '失败')], max_length=20, verbose_name='操作状态')),
                ('operation_flags', models.IntegerField(default=0, verbose_name='操作标志')),
                ('operation_params', models.JSONField(default=dict, verbose_name='操作参数')),
                ('result_message', models.TextField(blank=True, verbose_name='结果消息')),
                ('error_message', models.TextField(blank=True, verbose_name='错误消息')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('operator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作者')),
                ('snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='your_app.snapshot', verbose_name='快照')),
            ],
            options={
                'verbose_name': '快照操作',
                'verbose_name_plural': '快照操作',
                'db_table': 'vm_snapshot_operations',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='SnapshotMemory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('snapshot_mode', models.CharField(choices=[('no', '不保存内存'), ('internal', '内部保存'), ('external', '外部保存')], max_length=20, verbose_name='内存快照模式')),
                ('memory_file', models.CharField(blank=True, max_length=500, verbose_name='内存文件路径')),
                ('memory_size', models.BigIntegerField(blank=True, null=True, verbose_name='内存大小')),
                ('snapshot', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='memory', to='your_app.snapshot', verbose_name='快照')),
            ],
            options={
                'verbose_name': '快照内存',
                'verbose_name_plural': '快照内存',
                'db_table': 'vm_snapshot_memory',
            },
        ),
        migrations.CreateModel(
            name='SnapshotDisk',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('disk_name', models.CharField(max_length=100, verbose_name='磁盘名称')),
                ('disk_target', models.CharField(max_length=100, verbose_name='磁盘目标')),
                ('snapshot_mode', models.CharField(choices=[('no', '不快照'), ('internal', '内部快照'), ('external', '外部快照'), ('manual', '手动快照')], max_length=20, verbose_name='快照模式')),
                ('disk_type', models.CharField(choices=[('file', '文件'), ('block', '块设备'), ('network', '网络存储')], default='file', max_length=20, verbose_name='磁盘类型')),
                ('source_file', models.CharField(max_length=500, verbose_name='源文件路径')),
                ('snapshot_file', models.CharField(blank=True, max_length=500, verbose_name='快照文件路径')),
                ('driver_type', models.CharField(default='qcow2', max_length=50, verbose_name='驱动类型')),
                ('driver_name', models.CharField(default='qemu', max_length=50, verbose_name='驱动名称')),
                ('disk_size', models.BigIntegerField(blank=True, null=True, verbose_name='磁盘大小')),
                ('security_label_model', models.CharField(blank=True, max_length=50, verbose_name='安全标签模型')),
                ('security_label_relabel', models.BooleanField(default=True, verbose_name='安全标签重标记')),
                ('snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disks', to='your_app.snapshot', verbose_name='快照')),
            ],
            options={
                'verbose_name': '快照磁盘',
                'verbose_name_plural': '快照磁盘',
                'db_table': 'vm_snapshot_disks',
            },
        ),
        migrations.AddConstraint(
            model_name='snapshotdisk',
            constraint=models.UniqueConstraint(fields=('snapshot', 'disk_name'), name='unique_snapshot_disk'),
        ),
        migrations.AddConstraint(
            model_name='snapshot',
            constraint=models.UniqueConstraint(fields=('virtual_machine', 'name'), name='unique_vm_snapshot_name'),
        ),
        migrations.AddIndex(
            model_name='snapshot',
            index=models.Index(fields=['virtual_machine', 'is_current'], name='vm_snapshots_vm_current_idx'),
        ),
        migrations.AddIndex(
            model_name='snapshot',
            index=models.Index(fields=['creation_time'], name='vm_snapshots_creation_time_idx'),
        ),
        migrations.AddIndex(
            model_name='snapshot',
            index=models.Index(fields=['parent'], name='vm_snapshots_parent_idx'),
        ),
    ]
