"""
libvirt虚拟机快照数据表设计
支持完整的快照管理功能，包括快照树结构、磁盘快照、内存快照等
"""

from django.db import models
from django.contrib.auth.models import User
import uuid


class VirtualMachine(models.Model):
    """虚拟机基础信息表"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True, verbose_name="虚拟机名称")
    uuid = models.UUIDField(unique=True, verbose_name="libvirt UUID")
    hypervisor_uri = models.CharField(max_length=255, verbose_name="Hypervisor连接URI")
    status = models.CharField(max_length=20, choices=[
        ('running', '运行中'),
        ('paused', '暂停'),
        ('shutoff', '关闭'),
        ('crashed', '崩溃'),
    ], verbose_name="虚拟机状态")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vm_virtual_machines'
        verbose_name = "虚拟机"
        verbose_name_plural = "虚拟机"


class Snapshot(models.Model):
    """快照主表"""
    SNAPSHOT_TYPES = [
        ('disk_only', '仅磁盘快照'),
        ('memory_only', '仅内存快照'),
        ('full_system', '完整系统快照'),
    ]
    
    SNAPSHOT_STATES = [
        ('running', '运行中'),
        ('paused', '暂停'),
        ('shutoff', '关闭'),
        ('disk-snapshot', '磁盘快照'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, verbose_name="快照名称")
    description = models.TextField(blank=True, verbose_name="快照描述")
    
    # 关联虚拟机
    virtual_machine = models.ForeignKey(
        VirtualMachine, 
        on_delete=models.CASCADE, 
        related_name='snapshots',
        verbose_name="虚拟机"
    )
    
    # 快照类型和状态
    snapshot_type = models.CharField(
        max_length=20, 
        choices=SNAPSHOT_TYPES,
        verbose_name="快照类型"
    )
    state = models.CharField(
        max_length=20, 
        choices=SNAPSHOT_STATES,
        verbose_name="快照时虚拟机状态"
    )
    
    # 快照树结构
    parent = models.ForeignKey(
        'self', 
        null=True, 
        blank=True, 
        on_delete=models.CASCADE,
        related_name='children',
        verbose_name="父快照"
    )
    is_current = models.BooleanField(default=False, verbose_name="是否为当前快照")
    
    # 时间信息
    creation_time = models.DateTimeField(verbose_name="创建时间")
    created_at = models.DateTimeField(auto_now_add=True)
    
    # libvirt相关信息
    libvirt_xml = models.TextField(verbose_name="libvirt XML配置")
    domain_xml = models.TextField(blank=True, verbose_name="域XML配置")
    cookie = models.TextField(blank=True, verbose_name="快照Cookie数据")
    
    # 创建者信息
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True,
        verbose_name="创建者"
    )
    
    # 快照文件路径（用于外部快照）
    snapshot_path = models.CharField(
        max_length=500, 
        blank=True,
        verbose_name="快照文件路径"
    )
    
    # 快照大小（字节）
    size_bytes = models.BigIntegerField(null=True, blank=True, verbose_name="快照大小")
    
    # 删除状态
    deletion_in_progress = models.BooleanField(
        default=False, 
        verbose_name="删除进行中"
    )

    class Meta:
        db_table = 'vm_snapshots'
        verbose_name = "快照"
        verbose_name_plural = "快照"
        unique_together = [['virtual_machine', 'name']]
        indexes = [
            models.Index(fields=['virtual_machine', 'is_current']),
            models.Index(fields=['creation_time']),
            models.Index(fields=['parent']),
        ]


class SnapshotDisk(models.Model):
    """快照磁盘信息表"""
    SNAPSHOT_MODES = [
        ('no', '不快照'),
        ('internal', '内部快照'),
        ('external', '外部快照'),
        ('manual', '手动快照'),
    ]
    
    DISK_TYPES = [
        ('file', '文件'),
        ('block', '块设备'),
        ('network', '网络存储'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    snapshot = models.ForeignKey(
        Snapshot, 
        on_delete=models.CASCADE, 
        related_name='disks',
        verbose_name="快照"
    )
    
    # 磁盘标识
    disk_name = models.CharField(max_length=100, verbose_name="磁盘名称")  # 如 vda, sda
    disk_target = models.CharField(max_length=100, verbose_name="磁盘目标")
    
    # 快照模式
    snapshot_mode = models.CharField(
        max_length=20, 
        choices=SNAPSHOT_MODES,
        verbose_name="快照模式"
    )
    
    # 磁盘类型
    disk_type = models.CharField(
        max_length=20, 
        choices=DISK_TYPES,
        default='file',
        verbose_name="磁盘类型"
    )
    
    # 源文件信息
    source_file = models.CharField(max_length=500, verbose_name="源文件路径")
    snapshot_file = models.CharField(
        max_length=500, 
        blank=True,
        verbose_name="快照文件路径"
    )
    
    # 驱动信息
    driver_type = models.CharField(
        max_length=50, 
        default='qcow2',
        verbose_name="驱动类型"
    )
    driver_name = models.CharField(
        max_length=50, 
        default='qemu',
        verbose_name="驱动名称"
    )
    
    # 磁盘大小
    disk_size = models.BigIntegerField(null=True, blank=True, verbose_name="磁盘大小")
    
    # 安全标签
    security_label_model = models.CharField(
        max_length=50, 
        blank=True,
        verbose_name="安全标签模型"
    )
    security_label_relabel = models.BooleanField(
        default=True,
        verbose_name="安全标签重标记"
    )

    class Meta:
        db_table = 'vm_snapshot_disks'
        verbose_name = "快照磁盘"
        verbose_name_plural = "快照磁盘"
        unique_together = [['snapshot', 'disk_name']]


class SnapshotMemory(models.Model):
    """快照内存信息表"""
    MEMORY_SNAPSHOT_MODES = [
        ('no', '不保存内存'),
        ('internal', '内部保存'),
        ('external', '外部保存'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    snapshot = models.OneToOneField(
        Snapshot, 
        on_delete=models.CASCADE, 
        related_name='memory',
        verbose_name="快照"
    )
    
    # 内存快照模式
    snapshot_mode = models.CharField(
        max_length=20, 
        choices=MEMORY_SNAPSHOT_MODES,
        verbose_name="内存快照模式"
    )
    
    # 内存文件路径（外部模式）
    memory_file = models.CharField(
        max_length=500, 
        blank=True,
        verbose_name="内存文件路径"
    )
    
    # 内存大小
    memory_size = models.BigIntegerField(null=True, blank=True, verbose_name="内存大小")

    class Meta:
        db_table = 'vm_snapshot_memory'
        verbose_name = "快照内存"
        verbose_name_plural = "快照内存"


class SnapshotOperation(models.Model):
    """快照操作日志表"""
    OPERATION_TYPES = [
        ('create', '创建'),
        ('delete', '删除'),
        ('revert', '恢复'),
        ('redefine', '重定义'),
    ]
    
    OPERATION_STATUS = [
        ('pending', '等待中'),
        ('running', '执行中'),
        ('success', '成功'),
        ('failed', '失败'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    snapshot = models.ForeignKey(
        Snapshot, 
        on_delete=models.CASCADE, 
        related_name='operations',
        verbose_name="快照"
    )
    
    operation_type = models.CharField(
        max_length=20, 
        choices=OPERATION_TYPES,
        verbose_name="操作类型"
    )
    status = models.CharField(
        max_length=20, 
        choices=OPERATION_STATUS,
        verbose_name="操作状态"
    )
    
    # 操作参数
    operation_flags = models.IntegerField(default=0, verbose_name="操作标志")
    operation_params = models.JSONField(default=dict, verbose_name="操作参数")
    
    # 结果信息
    result_message = models.TextField(blank=True, verbose_name="结果消息")
    error_message = models.TextField(blank=True, verbose_name="错误消息")
    
    # 时间信息
    started_at = models.DateTimeField(auto_now_add=True, verbose_name="开始时间")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    
    # 操作者
    operator = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True,
        verbose_name="操作者"
    )

    class Meta:
        db_table = 'vm_snapshot_operations'
        verbose_name = "快照操作"
        verbose_name_plural = "快照操作"
        ordering = ['-started_at']
