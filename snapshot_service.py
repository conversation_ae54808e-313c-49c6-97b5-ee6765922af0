"""
libvirt虚拟机快照管理服务
提供快照的创建、删除、恢复等功能
"""

import libvirt
import xml.etree.ElementTree as ET
from datetime import datetime
from django.utils import timezone
from django.db import transaction
from .models import VirtualMachine, Snapshot, SnapshotDisk, SnapshotMemory, SnapshotOperation


class SnapshotService:
    """快照管理服务类"""
    
    def __init__(self, hypervisor_uri='qemu:///system'):
        self.hypervisor_uri = hypervisor_uri
        self.conn = None
    
    def connect(self):
        """连接到hypervisor"""
        if not self.conn:
            self.conn = libvirt.open(self.hypervisor_uri)
        return self.conn
    
    def disconnect(self):
        """断开连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
    
    def create_snapshot(self, vm_id, snapshot_name, description="", 
                       snapshot_type='full_system', disk_configs=None, 
                       memory_config=None, user=None):
        """
        创建虚拟机快照
        
        Args:
            vm_id: 虚拟机ID
            snapshot_name: 快照名称
            description: 快照描述
            snapshot_type: 快照类型 ('disk_only', 'memory_only', 'full_system')
            disk_configs: 磁盘配置列表
            memory_config: 内存配置
            user: 创建用户
        
        Returns:
            Snapshot对象
        """
        try:
            with transaction.atomic():
                # 获取虚拟机信息
                vm = VirtualMachine.objects.get(id=vm_id)
                
                # 连接libvirt
                conn = self.connect()
                domain = conn.lookupByUUIDString(str(vm.uuid))
                
                # 构建快照XML
                snapshot_xml = self._build_snapshot_xml(
                    snapshot_name, description, snapshot_type, 
                    disk_configs, memory_config
                )
                
                # 创建操作记录
                operation = SnapshotOperation.objects.create(
                    snapshot=None,  # 稍后更新
                    operation_type='create',
                    status='running',
                    operation_flags=self._get_snapshot_flags(snapshot_type),
                    operation_params={
                        'snapshot_type': snapshot_type,
                        'disk_configs': disk_configs or [],
                        'memory_config': memory_config or {}
                    },
                    operator=user
                )
                
                try:
                    # 调用libvirt创建快照
                    flags = self._get_snapshot_flags(snapshot_type)
                    libvirt_snapshot = domain.snapshotCreateXML(snapshot_xml, flags)
                    
                    # 获取快照详细信息
                    snapshot_xml_desc = libvirt_snapshot.getXMLDesc(0)
                    snapshot_info = self._parse_snapshot_xml(snapshot_xml_desc)
                    
                    # 创建快照记录
                    snapshot = Snapshot.objects.create(
                        name=snapshot_name,
                        description=description,
                        virtual_machine=vm,
                        snapshot_type=snapshot_type,
                        state=snapshot_info.get('state', 'running'),
                        parent=self._get_current_snapshot(vm),
                        is_current=True,
                        creation_time=timezone.now(),
                        libvirt_xml=snapshot_xml_desc,
                        domain_xml=snapshot_info.get('domain_xml', ''),
                        cookie=snapshot_info.get('cookie', ''),
                        snapshot_path=snapshot_info.get('snapshot_path', ''),
                        size_bytes=snapshot_info.get('size_bytes'),
                        created_by=user
                    )
                    
                    # 更新之前的当前快照状态
                    Snapshot.objects.filter(
                        virtual_machine=vm, 
                        is_current=True
                    ).exclude(id=snapshot.id).update(is_current=False)
                    
                    # 创建磁盘快照记录
                    if disk_configs:
                        self._create_disk_snapshots(snapshot, disk_configs, snapshot_info)
                    
                    # 创建内存快照记录
                    if memory_config and snapshot_type in ['memory_only', 'full_system']:
                        self._create_memory_snapshot(snapshot, memory_config, snapshot_info)
                    
                    # 更新操作记录
                    operation.snapshot = snapshot
                    operation.status = 'success'
                    operation.result_message = f"快照 {snapshot_name} 创建成功"
                    operation.completed_at = timezone.now()
                    operation.save()
                    
                    return snapshot
                    
                except libvirt.libvirtError as e:
                    # 更新操作记录为失败
                    operation.status = 'failed'
                    operation.error_message = str(e)
                    operation.completed_at = timezone.now()
                    operation.save()
                    raise Exception(f"创建快照失败: {str(e)}")
                    
        except Exception as e:
            raise Exception(f"快照创建过程出错: {str(e)}")
        finally:
            self.disconnect()
    
    def delete_snapshot(self, snapshot_id, user=None):
        """删除快照"""
        try:
            with transaction.atomic():
                snapshot = Snapshot.objects.get(id=snapshot_id)
                
                # 标记删除进行中
                snapshot.deletion_in_progress = True
                snapshot.save()
                
                # 创建操作记录
                operation = SnapshotOperation.objects.create(
                    snapshot=snapshot,
                    operation_type='delete',
                    status='running',
                    operator=user
                )
                
                try:
                    # 连接libvirt并删除快照
                    conn = self.connect()
                    domain = conn.lookupByUUIDString(str(snapshot.virtual_machine.uuid))
                    libvirt_snapshot = domain.snapshotLookupByName(snapshot.name, 0)
                    
                    # 删除快照
                    libvirt_snapshot.delete(0)
                    
                    # 删除数据库记录
                    snapshot.delete()
                    
                    # 更新操作记录
                    operation.status = 'success'
                    operation.result_message = f"快照 {snapshot.name} 删除成功"
                    operation.completed_at = timezone.now()
                    operation.save()
                    
                except libvirt.libvirtError as e:
                    # 取消删除标记
                    snapshot.deletion_in_progress = False
                    snapshot.save()
                    
                    # 更新操作记录
                    operation.status = 'failed'
                    operation.error_message = str(e)
                    operation.completed_at = timezone.now()
                    operation.save()
                    
                    raise Exception(f"删除快照失败: {str(e)}")
                    
        except Exception as e:
            raise Exception(f"快照删除过程出错: {str(e)}")
        finally:
            self.disconnect()
    
    def revert_snapshot(self, snapshot_id, user=None):
        """恢复到指定快照"""
        try:
            with transaction.atomic():
                snapshot = Snapshot.objects.get(id=snapshot_id)
                
                # 创建操作记录
                operation = SnapshotOperation.objects.create(
                    snapshot=snapshot,
                    operation_type='revert',
                    status='running',
                    operator=user
                )
                
                try:
                    # 连接libvirt并恢复快照
                    conn = self.connect()
                    domain = conn.lookupByUUIDString(str(snapshot.virtual_machine.uuid))
                    libvirt_snapshot = domain.snapshotLookupByName(snapshot.name, 0)
                    
                    # 恢复快照
                    domain.revertToSnapshot(libvirt_snapshot, 0)
                    
                    # 更新当前快照状态
                    Snapshot.objects.filter(
                        virtual_machine=snapshot.virtual_machine
                    ).update(is_current=False)
                    
                    snapshot.is_current = True
                    snapshot.save()
                    
                    # 更新操作记录
                    operation.status = 'success'
                    operation.result_message = f"成功恢复到快照 {snapshot.name}"
                    operation.completed_at = timezone.now()
                    operation.save()
                    
                except libvirt.libvirtError as e:
                    # 更新操作记录
                    operation.status = 'failed'
                    operation.error_message = str(e)
                    operation.completed_at = timezone.now()
                    operation.save()
                    
                    raise Exception(f"恢复快照失败: {str(e)}")
                    
        except Exception as e:
            raise Exception(f"快照恢复过程出错: {str(e)}")
        finally:
            self.disconnect()
    
    def _build_snapshot_xml(self, name, description, snapshot_type, disk_configs, memory_config):
        """构建快照XML配置"""
        root = ET.Element('domainsnapshot')
        
        # 基本信息
        name_elem = ET.SubElement(root, 'name')
        name_elem.text = name
        
        if description:
            desc_elem = ET.SubElement(root, 'description')
            desc_elem.text = description
        
        # 内存配置
        if memory_config and snapshot_type in ['memory_only', 'full_system']:
            memory_elem = ET.SubElement(root, 'memory')
            memory_elem.set('snapshot', memory_config.get('mode', 'internal'))
            if memory_config.get('file'):
                memory_elem.set('file', memory_config['file'])
        
        # 磁盘配置
        if disk_configs and snapshot_type in ['disk_only', 'full_system']:
            disks_elem = ET.SubElement(root, 'disks')
            for disk_config in disk_configs:
                disk_elem = ET.SubElement(disks_elem, 'disk')
                disk_elem.set('name', disk_config['name'])
                disk_elem.set('snapshot', disk_config.get('mode', 'external'))
                
                if disk_config.get('source_file'):
                    source_elem = ET.SubElement(disk_elem, 'source')
                    source_elem.set('file', disk_config['source_file'])
                
                if disk_config.get('driver_type'):
                    driver_elem = ET.SubElement(disk_elem, 'driver')
                    driver_elem.set('type', disk_config['driver_type'])
        
        return ET.tostring(root, encoding='unicode')
    
    def _get_snapshot_flags(self, snapshot_type):
        """获取快照创建标志"""
        flags = 0
        if snapshot_type == 'disk_only':
            flags |= libvirt.VIR_DOMAIN_SNAPSHOT_CREATE_DISK_ONLY
        return flags
    
    def _parse_snapshot_xml(self, xml_desc):
        """解析快照XML描述"""
        root = ET.fromstring(xml_desc)
        info = {}
        
        # 解析状态
        state_elem = root.find('state')
        if state_elem is not None:
            info['state'] = state_elem.text
        
        # 解析域XML
        domain_elem = root.find('domain')
        if domain_elem is not None:
            info['domain_xml'] = ET.tostring(domain_elem, encoding='unicode')
        
        # 解析Cookie
        cookie_elem = root.find('cookie')
        if cookie_elem is not None:
            info['cookie'] = cookie_elem.text
        
        return info
    
    def _get_current_snapshot(self, vm):
        """获取虚拟机当前快照"""
        try:
            return Snapshot.objects.get(virtual_machine=vm, is_current=True)
        except Snapshot.DoesNotExist:
            return None
    
    def _create_disk_snapshots(self, snapshot, disk_configs, snapshot_info):
        """创建磁盘快照记录"""
        for disk_config in disk_configs:
            SnapshotDisk.objects.create(
                snapshot=snapshot,
                disk_name=disk_config['name'],
                disk_target=disk_config.get('target', disk_config['name']),
                snapshot_mode=disk_config.get('mode', 'external'),
                disk_type=disk_config.get('type', 'file'),
                source_file=disk_config.get('source_file', ''),
                snapshot_file=disk_config.get('snapshot_file', ''),
                driver_type=disk_config.get('driver_type', 'qcow2'),
                driver_name=disk_config.get('driver_name', 'qemu')
            )
    
    def _create_memory_snapshot(self, snapshot, memory_config, snapshot_info):
        """创建内存快照记录"""
        SnapshotMemory.objects.create(
            snapshot=snapshot,
            snapshot_mode=memory_config.get('mode', 'internal'),
            memory_file=memory_config.get('file', ''),
            memory_size=memory_config.get('size')
        )
